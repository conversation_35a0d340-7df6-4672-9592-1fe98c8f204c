<?php

namespace App\Livewire;

use App\Models\Ad;
use App\Models\AdReport;
use App\Models\Message;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('layouts.app')]
class ShowAd extends Component
{
    use AuthorizesRequests;

    public Ad $ad;
    public $similarAds = [];
    public $userOtherAds = [];
    public $showContactModal = false;
    public $showReportModal = false;
    public $message = '';
    public $reportReason = '';
    public $reportDescription = '';

    public function mount(Ad $ad)
    {
        $this->ad = $ad;
        $this->ad->load(['images', 'user', 'category']);

        // Get similar ads (same category, excluding this one)
        $this->similarAds = Ad::where('category_id', $this->ad->category_id)
            ->where('id', '!=', $this->ad->id)
            ->where(function($query) {
                $query->where('expires_at', '>', now())
                      ->orWhereNull('expires_at');
            })
            ->with('images')
            ->latest()
            ->take(3)
            ->get();

        // Get other ads from the same user (excluding this one)
        $this->userOtherAds = Ad::where('user_id', $this->ad->user_id)
            ->where('id', '!=', $this->ad->id)
            ->where(function($query) {
                $query->where('expires_at', '>', now())
                      ->orWhereNull('expires_at');
            })
            ->with('images')
            ->latest()
            ->take(4)
            ->get();
    }

    public function delete()
    {
        $this->authorize('delete', $this->ad);

        $this->ad->delete();

        session()->flash('success', 'آگهی با موفقیت حذف شد.');
        return redirect()->route('ads.index');
    }

    public function renewAd()
    {
        if (!auth()->check() || (auth()->id() !== $this->ad->user_id && auth()->user()->role_id != 1)) {
            session()->flash('error', 'شما اجازه تمدید این آگهی را ندارید.');
            return;
        }

        // If the ad has a package, redirect to payment
        if ($this->ad->package_id) {
            return redirect()->route('payment.create', ['ad' => $this->ad->id]);
        } else {
            // For free ads, just extend the expiration date
            $this->ad->update([
                'expires_at' => Carbon::now()->addDays(30),
            ]);

            session()->flash('success', 'آگهی شما با موفقیت تمدید شد.');
            $this->ad->refresh();
        }
    }

    public function showContactForm()
    {
        if (!auth()->check()) {
            session()->flash('error', 'برای ارسال پیام باید وارد حساب کاربری خود شوید.');
            return redirect()->route('login');
        }

        $this->showContactModal = true;
    }

    public function hideContactForm()
    {
        $this->showContactModal = false;
        $this->message = '';
    }

    public function sendMessage()
    {
        if (!auth()->check()) {
            session()->flash('error', 'برای ارسال پیام باید وارد حساب کاربری خود شوید.');
            return redirect()->route('login');
        }

        $this->validate([
            'message' => 'required|string|min:10|max:1000',
        ]);

        // Create a new message
        Message::create([
            'sender_id' => auth()->id(),
            'receiver_id' => $this->ad->user_id,
            'ad_id' => $this->ad->id,
            'message' => $this->message,
        ]);

        session()->flash('success', 'پیام شما با موفقیت ارسال شد.');
        $this->hideContactForm();
    }

    public function reportAd()
    {
        if (!auth()->check()) {
            session()->flash('error', 'برای گزارش آگهی باید وارد حساب کاربری خود شوید.');
            return redirect()->route('login');
        }

        $this->showReportModal = true;
    }

    public function hideReportForm()
    {
        $this->showReportModal = false;
        $this->reportReason = '';
        $this->reportDescription = '';
    }

    public function submitReport()
    {
        if (!auth()->check()) {
            session()->flash('error', 'برای گزارش آگهی باید وارد حساب کاربری خود شوید.');
            return redirect()->route('login');
        }

        $this->validate([
            'reportReason' => 'required|string|in:fake,offensive,sold,other',
            'reportDescription' => $this->reportReason === 'other' ? 'required|string|min:10|max:1000' : '',
        ]);

        // Create a new report
        AdReport::create([
            'ad_id' => $this->ad->id,
            'user_id' => auth()->id(),
            'reason' => $this->reportReason,
            'description' => $this->reportDescription,
        ]);

        session()->flash('success', 'گزارش شما با موفقیت ثبت شد. با تشکر از همکاری شما.');
        $this->hideReportForm();
    }

    public function render()
    {
        return view('livewire.show-ad');
    }
}
