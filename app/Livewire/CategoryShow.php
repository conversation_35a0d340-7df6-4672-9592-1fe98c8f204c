<?php

namespace App\Livewire;

use App\Models\Ad;
use App\Models\Category;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\WithPagination;

class CategoryShow extends Component
{
    use WithPagination;

    public Category $category;
    public $search = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 12;
    public $relatedCategories;

    protected $queryString = [
        'search' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
        'perPage' => ['except' => 12],
    ];

    public function mount($slug)
    {
        $this->category = Category::where('slug', $slug)->firstOrFail();
        
        // Load subcategories with ad count
        $this->category->load(['children' => function ($query) {
            $query->withCount('ads');
        }]);
        
        // Get related categories (siblings or parent's siblings)
        if ($this->category->parent_id) {
            // Get siblings (other categories with the same parent)
            $this->relatedCategories = Category::where('parent_id', $this->category->parent_id)
                ->where('id', '!=', $this->category->id)
                ->take(8)
                ->get();
        } else {
            // Get other root categories
            $this->relatedCategories = Category::whereNull('parent_id')
                ->where('id', '!=', $this->category->id)
                ->take(8)
                ->get();
        }
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function render()
    {
        // Get category IDs (current category and all its descendants)
        $categoryIds = collect([$this->category->id]);
        
        if ($this->category->children->count() > 0) {
            $categoryIds = $categoryIds->merge($this->category->children->pluck('id'));
        }
        
        // Build the query
        $query = Ad::whereIn('category_id', $categoryIds)
            ->where(function ($query) {
                $query->where('expires_at', '>', Carbon::now())
                    ->orWhereNull('expires_at');
            });
        
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('title', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%')
                  ->orWhere('location', 'like', '%' . $this->search . '%');
            });
        }
        
        // First get premium ads
        $premiumAds = (clone $query)->where('is_premium', true)
            ->orderBy($this->sortField, $this->sortDirection)
            ->take(3)
            ->get();
        
        // Then get featured ads
        $featuredAds = (clone $query)->where('is_featured', true)
            ->where('is_premium', false)
            ->orderBy($this->sortField, $this->sortDirection)
            ->take(6)
            ->get();
        
        // Get remaining ads
        $regularQuery = (clone $query)->where('is_premium', false)
            ->where('is_featured', false)
            ->orderBy($this->sortField, $this->sortDirection);
        
        // Combine all ads
        $premiumIds = $premiumAds->pluck('id')->toArray();
        $featuredIds = $featuredAds->pluck('id')->toArray();
        $excludeIds = array_merge($premiumIds, $featuredIds);
        
        if (!empty($excludeIds)) {
            $regularQuery->whereNotIn('id', $excludeIds);
        }
        
        $regularAds = $regularQuery->paginate($this->perPage);
        
        // Merge collections for the first page
        if ($this->page == 1) {
            $mergedAds = $premiumAds->concat($featuredAds)->concat($regularAds->getCollection());
            $regularAds->setCollection($mergedAds);
        }
        
        return view('livewire.category-show', [
            'ads' => $regularAds,
        ]);
    }
}
