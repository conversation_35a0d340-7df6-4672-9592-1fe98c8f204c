<?php

namespace App\Livewire\Admin\Packages;

use App\Models\Package;
use Livewire\Component;

class Create extends Component
{
    public $name;
    public $description;
    public $price;
    public $duration_days;
    public $is_featured = false;
    public $is_premium = false;
    public $is_active = true;

    protected $rules = [
        'name' => 'required|string|max:255',
        'description' => 'nullable|string',
        'price' => 'required|numeric|min:0',
        'duration_days' => 'required|integer|min:1',
        'is_featured' => 'boolean',
        'is_premium' => 'boolean',
        'is_active' => 'boolean',
    ];

    protected $messages = [
        'name.required' => 'نام پکیج الزامی است.',
        'price.required' => 'قیمت الزامی است.',
        'price.numeric' => 'قیمت باید عدد باشد.',
        'price.min' => 'قیمت نمی‌تواند منفی باشد.',
        'duration_days.required' => 'مدت زمان الزامی است.',
        'duration_days.integer' => 'مدت زمان باید عدد صحیح باشد.',
        'duration_days.min' => 'مدت زمان باید حداقل 1 روز باشد.',
    ];



    public function save()
    {
        $this->validate();

        Package::create([
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'duration_days' => $this->duration_days,
            'is_featured' => $this->is_featured,
            'is_premium' => $this->is_premium,
            'is_active' => $this->is_active,
        ]);

        session()->flash('success', 'پکیج با موفقیت ایجاد شد.');
        return redirect()->route('admin.packages.index');
    }

    public function render()
    {
        return view('livewire.admin.packages.create');
    }
}
