<?php

namespace App\Livewire\Admin\Packages;

use App\Models\Package;
use Livewire\Component;

class Edit extends Component
{
    public Package $package;
    public $name;
    public $description;
    public $price;
    public $duration_days;
    public $is_featured;
    public $is_premium;
    public $is_active;
    public $features = [];
    public $newFeature = '';

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'duration_days' => 'required|integer|min:1',
            'is_featured' => 'boolean',
            'is_premium' => 'boolean',
            'is_active' => 'boolean',
            'features' => 'array',
            'features.*' => 'string|max:255',
        ];
    }

    protected $messages = [
        'name.required' => 'نام پکیج الزامی است.',
        'price.required' => 'قیمت الزامی است.',
        'price.numeric' => 'قیمت باید عدد باشد.',
        'price.min' => 'قیمت نمی‌تواند منفی باشد.',
        'duration_days.required' => 'مدت زمان الزامی است.',
        'duration_days.integer' => 'مدت زمان باید عدد صحیح باشد.',
        'duration_days.min' => 'مدت زمان باید حداقل 1 روز باشد.',
    ];

    public function mount(Package $package)
    {
        $this->package = $package;
        $this->package->load(['ads']);
        
        $this->name = $package->name;
        $this->description = $package->description;
        $this->price = $package->price;
        $this->duration_days = $package->duration_days;
        $this->is_featured = $package->is_featured;
        $this->is_premium = $package->is_premium;
        $this->is_active = $package->is_active;
        $this->features = json_decode($package->features, true) ?: [];
    }

    public function addFeature()
    {
        if (!empty($this->newFeature)) {
            $this->features[] = $this->newFeature;
            $this->newFeature = '';
        }
    }

    public function removeFeature($index)
    {
        if (isset($this->features[$index])) {
            unset($this->features[$index]);
            $this->features = array_values($this->features); // Re-index array
        }
    }

    public function confirmDelete()
    {
        $this->dispatch('showDeleteModal');
    }

    public function deletePackage()
    {
        $this->package->delete();
        session()->flash('success', 'پکیج با موفقیت حذف شد.');
        return redirect()->route('admin.packages.index');
    }

    public function save()
    {
        $this->validate();

        $this->package->update([
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'duration_days' => $this->duration_days,
            'is_featured' => $this->is_featured,
            'is_premium' => $this->is_premium,
            'is_active' => $this->is_active,
            'features' => json_encode($this->features),
        ]);

        session()->flash('success', 'پکیج با موفقیت بروزرسانی شد.');
        return redirect()->route('admin.packages.index');
    }

    public function render()
    {
        return view('livewire.admin.packages.edit')->layout('layouts.admin');
    }
}
