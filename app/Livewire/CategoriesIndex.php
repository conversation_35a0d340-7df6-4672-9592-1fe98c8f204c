<?php

namespace App\Livewire;

use App\Models\Category;
use Livewire\Component;

class CategoriesIndex extends Component
{
    public $featuredCategories;
    public $mainCategories;

    public function mount()
    {
        // Get featured categories with ad count
        $this->featuredCategories = Category::withCount('ads')
            ->where('is_featured', true)
            ->orderBy('order')
            ->get();
        
        // Get main categories (root categories) with their children and ad count
        $this->mainCategories = Category::withCount('ads')
            ->with(['children' => function ($query) {
                $query->withCount('ads')->orderBy('order');
            }])
            ->whereNull('parent_id')
            ->orderBy('order')
            ->get();
    }

    public function render()
    {
        return view('livewire.categories-index');
    }
}
