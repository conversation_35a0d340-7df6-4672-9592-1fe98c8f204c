<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Ad extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'title',
        'description',
        'price',
        'location',
        'category_id',
        'user_id',
        'type',
        'package_id',
        'is_featured',
        'is_premium',
        'expires_at',
    ];

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function package()
    {
        return $this->belongsTo(Package::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function scopeForUser($query)
    {
        return $query->where('user_id', auth()->id());
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    public function scopeActive($query)
    {
        return $query->where('expires_at', '>', now())->orWhereNull('expires_at');
    }

    /**
     * Get the images for the ad.
     */
    public function images()
    {
        return $this->hasMany(AdImage::class)->orderBy('order');
    }

    /**
     * Get the primary image for the ad.
     */
    public function primaryImage()
    {
        return $this->hasOne(AdImage::class)->where('is_primary', true);
    }

    /**
     * Get the image URL for the ad.
     */
    public function getImageUrlAttribute()
    {
        if ($this->primaryImage) {
            return asset('storage/' . $this->primaryImage->image_path);
        }

        if ($this->images->count() > 0) {
            return asset('storage/' . $this->images->first()->image_path);
        }

        return asset('images/no-image.jpg');
    }
}
