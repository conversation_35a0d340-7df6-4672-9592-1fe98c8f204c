<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Ad;
use App\Models\Category;
use App\Models\User;

class AdSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $category = Category::first(); // دریافت اولین دسته‌بندی
        $user = User::first(); // دریافت اولین کاربر

        Ad::create([
            'title' => 'فروش آپارتمان در تهران',
            'description' => 'یک آپارتمان 3 خوابه با منظره عالی',
            'price' => 1500000000,
            'location' => 'تهران',
            'category_id' => $category->id,
            'user_id' => $user->id,
        ]);

        $category = Category::find(2); // دریافت اولین دسته‌بندی

        Ad::create([
            'title' => 'فروش خودرو پژو 206',
            'description' => 'یک خودرو تمیز و کم کارکرد',
            'price' => 250000000,
            'location' => 'کرج',
            'category_id' => $category->id,
            'user_id' => $user->id,
        ]);

        $user = User::find(2);// دریافت اولین کاربر
        Ad::create([
            'title' => 'فروش خودرو پژو 207',
            'description' => 'یک خودرو تمیز و کم کارکرد',
            'price' => 890000000,
            'location' => 'تهران',
            'category_id' => $category->id,
            'user_id' => $user->id,
        ]);

        $user = User::find(3);// دریافت اولین کاربر
        Ad::create([
            'title' => 'ماکسیما',
            'description' => 'یک خودرو تمیز و کم کارکرد',
            'price' => 1125000000,
            'location' => 'شیراز',
            'category_id' => $category->id,
            'user_id' => $user->id,
        ]);
    }
}
