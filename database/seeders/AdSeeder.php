<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Ad;
use App\Models\Category;
use App\Models\User;
use App\Models\Package;
use Carbon\Carbon;

class AdSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all();
        $users = User::all();
        $packages = Package::all();

        // آرایه‌ای از عناوین آگهی‌های مختلف
        $adTitles = [
            // املاک
            'فروش آپارتمان 3 خوابه در تهران',
            'اجاره خانه ویلایی در شمال تهران',
            'فروش زمین مسکونی در کرج',
            'اجاره دفتر کار در مرکز شهر',
            'فروش مغازه در خیابان اصلی',
            'اجاره آپارتمان مبله در شیراز',
            'فروش ویلا در شهرک غرب',
            'اجاره انبار در جنوب تهران',

            // خودرو
            'فروش پژو 206 مدل 1400',
            'فروش سمند EF7 دوگانه',
            'فروش پراید 131 تمیز',
            'فروش دنا پلاس توربو',
            'فروش تیبا 2 هاچبک',
            'فروش ریو هاچبک',
            'فروش پژو 207 اتوماتیک',
            'فروش ساینا دنده‌ای',

            // لوازم خانگی
            'فروش یخچال فریزر سامسونگ',
            'فروش ماشین لباسشویی ال جی',
            'فروش تلویزیون 55 اینچ',
            'فروش کولر گازی اسپلیت',
            'فروش مبل راحتی 7 نفره',
            'فروش میز ناهارخوری چوبی',
            'فروش جاروبرقی بوش',
            'فروش اجاق گاز 5 شعله',

            // موبایل و تبلت
            'فروش آیفون 13 پرو مکس',
            'فروش سامسونگ گلکسی S22',
            'فروش شیائومی ردمی نوت 11',
            'فروش هواوی P40 لایت',
            'فروش آیپد ایر نسل 4',
            'فروش تبلت سامسونگ تب A8',
            'فروش گوشی نوکیا کلاسیک',
            'فروش آیفون SE نسل 2',

            // لپ‌تاپ و کامپیوتر
            'فروش لپ‌تاپ ایسوس گیمینگ',
            'فروش مک‌بوک پرو 13 اینچ',
            'فروش کامپیوتر دسکتاپ گیمینگ',
            'فروش لپ‌تاپ لنوو تینک‌پد',
            'فروش مانیتور 27 اینچ',
            'فروش کیبورد و ماوس گیمینگ',
            'فروش پرینتر لیزری HP',
            'فروش هارد اکسترنال 2 ترابایت',
        ];

        $descriptions = [
            'در حد نو، تمیز و بدون خرابی',
            'کیفیت عالی، قیمت مناسب',
            'فوری فروش، قیمت توافقی',
            'تست شده، گارانتی معتبر',
            'کم استفاده، نگهداری شده',
            'قیمت ثابت، جای چانه زدن نیست',
            'فروش به دلیل مهاجرت',
            'تحویل فوری، پرداخت نقدی',
            'شرایط پرداخت آسان',
            'قابل تعویض با کالای مشابه',
        ];

        $locations = [
            'تهران', 'کرج', 'اصفهان', 'شیراز', 'مشهد', 'تبریز',
            'اهواز', 'کرمان', 'رشت', 'قم', 'زاهدان', 'همدان',
            'یزد', 'اردبیل', 'بندرعباس', 'کرمانشاه', 'گرگان', 'ساری'
        ];

        // ایجاد 100 آگهی
        for ($i = 0; $i < 100; $i++) {
            $category = $categories->random();
            $user = $users->random();
            $package = $packages->random();

            // تعیین نوع آگهی بر اساس پکیج
            $type = 'basic';
            $isFeatured = false;
            $isPremium = false;

            if ($package->is_premium) {
                $type = 'premium';
                $isPremium = true;
                $isFeatured = true;
            } elseif ($package->is_featured) {
                $type = 'featured';
                $isFeatured = true;
            } elseif ($package->name === 'نردبانی') {
                $type = 'ladder';
            }

            // تاریخ انقضا
            $expiresAt = Carbon::now()->addDays($package->duration_days);

            Ad::create([
                'title' => $adTitles[array_rand($adTitles)],
                'description' => $descriptions[array_rand($descriptions)] . '. ' . fake()->paragraph(2),
                'price' => fake()->numberBetween(100000, 5000000000),
                'location' => $locations[array_rand($locations)],
                'category_id' => $category->id,
                'user_id' => $user->id,
                'package_id' => $package->price > 0 ? $package->id : null,
                'type' => $type,
                'is_featured' => $isFeatured,
                'is_premium' => $isPremium,
                'expires_at' => $expiresAt,
                'created_at' => fake()->dateTimeBetween('-60 days', 'now'),
                'updated_at' => fake()->dateTimeBetween('-30 days', 'now'),
            ]);
        }
    }
}
