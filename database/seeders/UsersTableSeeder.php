<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        // INSERT INTO `users` (`id`, `role_id`, `name`, `mobile`, `email`, `avatar`, `email_verified_at`, `password`, `remember_token`, `settings`, `created_at`, `updated_at`) VALUES (NULL, '1', 'Mohammad', NULL, '<EMAIL>', 'users/default.png', NULL, '$2y$12$PIO0DoBnRIs/lwNSsVl6feuzK7ElgkXu7xbVpke.Qxi.eNKOEGNXy', NULL, NULL, '2025-01-29 10:22:46', '2025-01-29 10:22:46')

        User::firstOrCreate([
            'name' => '<PERSON>',
            'role_id' => '1',
            'email' => '<EMAIL>',
            'mobile' => '09902520250',
            'avatar' => 'users/default.png',
            'password' => bcrypt('mnba110'),
        ]);

        User::firstOrCreate([
            'name' => 'زهرا حسینی',
            'role_id' => '2',
            'mobile' => '09177777777',
            'email' => '<EMAIL>',
            'password' => bcrypt('123'),
        ]);

        User::firstOrCreate([
            'name' => 'علی احمدی',
            'role_id' => '2',
            'mobile' => '09122222222',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);
    }
}
