<?php

namespace Database\Seeders;

use App\Models\Favorite;
use App\Models\Ad;
use App\Models\User;
use Illuminate\Database\Seeder;

class FavoriteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $ads = Ad::all();
        $users = User::all();
        
        // هر کاربر 3 تا 15 آگهی را به علاقه‌مندی‌هایش اضافه می‌کند
        foreach ($users as $user) {
            $favoriteCount = rand(3, 15);
            $selectedAds = $ads->random($favoriteCount);
            
            foreach ($selectedAds as $ad) {
                // اطمینان از اینکه کاربر آگهی خودش را به علاقه‌مندی‌ها اضافه نکند
                if ($ad->user_id === $user->id) {
                    continue;
                }
                
                // اطمینان از اینکه قبلاً اضافه نشده باشد
                $existingFavorite = Favorite::where('user_id', $user->id)
                                          ->where('ad_id', $ad->id)
                                          ->first();
                
                if ($existingFavorite) {
                    continue;
                }
                
                Favorite::create([
                    'user_id' => $user->id,
                    'ad_id' => $ad->id,
                    'created_at' => fake()->dateTimeBetween('-30 days', 'now'),
                    'updated_at' => fake()->dateTimeBetween('-30 days', 'now'),
                ]);
            }
        }
    }
}
