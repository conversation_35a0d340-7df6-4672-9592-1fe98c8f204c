<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>سایت آگهی</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    @livewireStyles
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">سایت آگهی</a>
            
            <div class="navbar-nav ms-auto">
                @auth
                    <a class="nav-link" href="{{ route('user.dashboard') }}">داشبورد</a>
                    <a class="nav-link" href="{{ route('ads.create') }}">ثبت آگهی</a>
                    <a class="nav-link" href="{{ route('logout') }}">خروج</a>
                @else
                    <a class="nav-link" href="{{ route('login') }}">ورود</a>
                    <a class="nav-link" href="{{ route('register') }}">ثبت نام</a>
                @endauth
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        @if (session()->has('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        @if (session()->has('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        {{ $slot }}
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    @livewireScripts
</body>
</html>
