<div>
    <div class="container">
        <div class="row mb-4">
            <div class="col-md-12">
                <h1 class="text-center">دسته‌بندی‌های آگهی</h1>
                <p class="text-center lead">دسته‌بندی مورد نظر خود را انتخاب کنید</p>
            </div>
        </div>

        <div class="row mb-5">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">دسته‌بندی‌های ویژه</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($featuredCategories as $category)
                                <div class="col-md-3 mb-4">
                                    <a href="{{ route('category.show', $category->slug) }}" class="card h-100 text-decoration-none">
                                        <div class="card-body text-center">
                                            @if($category->icon)
                                                <i class="fas {{ $category->icon }} fa-4x mb-3 text-primary"></i>
                                            @endif
                                            <h4 class="card-title">{{ $category->name }}</h4>
                                            <p class="card-text text-muted">{{ $category->ads_count }} آگهی</p>
                                        </div>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @foreach($mainCategories as $mainCategory)
            <div class="row mb-5">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h3 class="mb-0">
                                @if($mainCategory->icon)
                                    <i class="fas {{ $mainCategory->icon }} me-2"></i>
                                @endif
                                {{ $mainCategory->name }}
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="{{ route('category.show', $mainCategory->slug) }}" class="card h-100 bg-light text-decoration-none">
                                        <div class="card-body text-center">
                                            @if($mainCategory->icon)
                                                <i class="fas {{ $mainCategory->icon }} fa-3x mb-3 text-info"></i>
                                            @endif
                                            <h5 class="card-title">همه آگهی‌های {{ $mainCategory->name }}</h5>
                                            <p class="card-text text-muted">{{ $mainCategory->ads_count }} آگهی</p>
                                        </div>
                                    </a>
                                </div>

                                @foreach($mainCategory->children as $subCategory)
                                    <div class="col-md-3 mb-3">
                                        <a href="{{ route('category.show', $subCategory->slug) }}" class="card h-100 text-decoration-none">
                                            <div class="card-body text-center">
                                                @if($subCategory->icon)
                                                    <i class="fas {{ $subCategory->icon }} fa-2x mb-3 text-info"></i>
                                                @endif
                                                <h5 class="card-title">{{ $subCategory->name }}</h5>
                                                <p class="card-text text-muted">{{ $subCategory->ads_count }} آگهی</p>
                                            </div>
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach

        <div class="row">
            <div class="col-md-12 text-center">
                <a href="{{ route('ads.create') }}" class="btn btn-lg btn-primary">ثبت آگهی جدید</a>
            </div>
        </div>
    </div>
</div>
