<div>
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3>لیست آگهی‌ها</h3>
            @if(auth()->check())
            <a href="{{ route('ads.create') }}" class="btn btn-primary">ثبت آگهی جدید</a>
            @endif
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-4">
                    <input type="text" wire:model.live="search" class="form-control" placeholder="جستجو...">
                </div>
                <div class="col-md-3">
                    <select wire:model.live="categoryFilter" class="form-control">
                        <option value="">همه دسته‌بندی‌ها</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <select wire:model.live="sortField" class="form-control">
                        <option value="created_at">تاریخ ثبت</option>
                        <option value="price">قیمت</option>
                        <option value="title">عنوان</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select wire:model.live="sortDirection" class="form-control">
                        <option value="desc">نزولی</option>
                        <option value="asc">صعودی</option>
                    </select>
                </div>
            </div>

            @if($ads->count() > 0)
                <div class="row">
                    @foreach($ads as $ad)
                        <div class="col-md-4 mb-3">
                            <div class="card h-100 {{ $ad->is_premium ? 'border-warning' : ($ad->is_featured ? 'border-info' : '') }}">
                                <div class="card-header">
                                    <h5>
                                        <a href="{{ route('ads.show', $ad) }}">{{ $ad->title }}</a>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p>{{ \Illuminate\Support\Str::limit($ad->description, 100) }}</p>
                                    <p><strong>قیمت:</strong> {{ number_format($ad->price) }} تومان</p>
                                    <p><strong>موقعیت:</strong> {{ $ad->location }}</p>
                                    <p><strong>دسته‌بندی:</strong> {{ $ad->category->name }}</p>
                                </div>
                                <div class="card-footer">
                                    <small class="text-muted">تاریخ ثبت: {{ $ad->created_at->format('Y/m/d') }}</small>
                                    @if($ad->is_premium)
                                        <span class="badge bg-warning float-end">ویژه</span>
                                    @elseif($ad->is_featured)
                                        <span class="badge bg-info float-end">برجسته</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="mt-3">
                    {{ $ads->links() }}
                </div>
            @else
                <div class="alert alert-info">
                    هیچ آگهی‌ای یافت نشد.
                </div>
            @endif
        </div>
    </div>
</div>
